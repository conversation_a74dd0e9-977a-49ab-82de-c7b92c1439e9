version: '3.8'

services:
  # Web应用服务
  web:
    build: .
    container_name: smart-dispatch-web
    ports:
      - "8080:80"
    volumes:
      - ./public/uploads:/var/www/html/public/uploads
      - ./runtime:/var/www/html/runtime
    environment:
      - APP_DEBUG=false
      - DATABASE_TYPE=mysql
      # 使用宿主机的MySQL服务
      - DATABASE_HOSTNAME=host.docker.internal
      - DATABASE_PORT=3306
      - DATABASE_DATABASE=smart_dispatch
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=123456
      - DATABASE_PREFIX=fa_
      # 使用宿主机的Redis服务
      - REDIS_HOSTNAME=host.docker.internal
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
    extra_hosts:
      # 允许容器访问宿主机服务
      - "host.docker.internal:host-gateway"
    networks:
      - smart-dispatch-network



networks:
  smart-dispatch-network:
    driver: bridge
