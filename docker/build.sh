#!/bin/bash

# 智慧派单管理系统 Docker 构建脚本
# 作者: 智慧派单团队
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="smart-dispatch-system"
IMAGE_NAME="smart-dispatch"
VERSION="1.0.0"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  智慧派单管理系统 Docker 构建工具  ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: docker-compose 未安装，请先安装 docker-compose${NC}"
    exit 1
fi

# 显示菜单
show_menu() {
    echo -e "${YELLOW}请选择操作:${NC}"
    echo "1. 构建 Docker 镜像"
    echo "2. 启动服务 (内置数据库)"
    echo "3. 启动服务 (外部数据库)"
    echo "4. 停止服务"
    echo "5. 重新构建并启动 (内置数据库)"
    echo "6. 重新构建并启动 (外部数据库)"
    echo "7. 查看服务状态"
    echo "8. 查看日志"
    echo "9. 清理镜像和容器"
    echo "10. 配置外部数据库连接"
    echo "11. 退出"
    echo ""
}

# 构建镜像
build_image() {
    echo -e "${BLUE}开始构建 Docker 镜像...${NC}"
    docker build -t ${IMAGE_NAME}:${VERSION} -t ${IMAGE_NAME}:latest .
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 镜像构建成功！${NC}"
        echo -e "镜像名称: ${IMAGE_NAME}:${VERSION}"
        echo -e "镜像名称: ${IMAGE_NAME}:latest"
    else
        echo -e "${RED}✗ 镜像构建失败！${NC}"
        exit 1
    fi
}

# 启动服务（内置数据库）
start_services() {
    echo -e "${BLUE}启动服务（内置数据库）...${NC}"
    docker-compose up -d

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务启动成功！${NC}"
        echo ""
        echo -e "${YELLOW}服务访问地址:${NC}"
        echo -e "Web应用: http://localhost:8080"
        echo -e "phpMyAdmin: http://localhost:8081"
        echo ""
        echo -e "${YELLOW}内置数据库连接信息:${NC}"
        echo -e "主机: localhost"
        echo -e "端口: 3306"
        echo -e "用户名: root"
        echo -e "密码: 123456"
        echo -e "数据库: smart_dispatch"
    else
        echo -e "${RED}✗ 服务启动失败！${NC}"
        exit 1
    fi
}

# 启动服务（外部数据库）
start_services_external() {
    echo -e "${BLUE}启动服务（外部数据库）...${NC}"

    # 检查是否存在.env文件
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}未找到 .env 文件，将使用默认配置${NC}"
        echo -e "${YELLOW}建议先运行 '配置外部数据库连接' 选项${NC}"
    fi

    docker-compose -f docker-compose.external.yml up -d

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务启动成功！${NC}"
        echo ""
        echo -e "${YELLOW}服务访问地址:${NC}"
        echo -e "Web应用: http://localhost:8080"
        echo -e "phpMyAdmin: http://localhost:8081"
        echo ""
        echo -e "${YELLOW}注意: 请确保外部MySQL和Redis服务正在运行${NC}"
    else
        echo -e "${RED}✗ 服务启动失败！${NC}"
        echo -e "${RED}请检查外部数据库连接配置${NC}"
        exit 1
    fi
}

# 停止服务
stop_services() {
    echo -e "${BLUE}停止服务...${NC}"

    # 停止所有可能的配置
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.external.yml down 2>/dev/null || true

    echo -e "${GREEN}✓ 服务已停止！${NC}"
}

# 重新构建并启动（内置数据库）
rebuild_and_start() {
    echo -e "${BLUE}重新构建并启动服务（内置数据库）...${NC}"
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务重新构建并启动成功！${NC}"
        echo ""
        echo -e "${YELLOW}服务访问地址:${NC}"
        echo -e "Web应用: http://localhost:8080"
        echo -e "phpMyAdmin: http://localhost:8081"
    else
        echo -e "${RED}✗ 服务重新构建失败！${NC}"
        exit 1
    fi
}

# 重新构建并启动（外部数据库）
rebuild_and_start_external() {
    echo -e "${BLUE}重新构建并启动服务（外部数据库）...${NC}"
    docker-compose -f docker-compose.external.yml down
    docker-compose -f docker-compose.external.yml build --no-cache
    docker-compose -f docker-compose.external.yml up -d

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 服务重新构建并启动成功！${NC}"
        echo ""
        echo -e "${YELLOW}服务访问地址:${NC}"
        echo -e "Web应用: http://localhost:8080"
        echo -e "phpMyAdmin: http://localhost:8081"
    else
        echo -e "${RED}✗ 服务重新构建失败！${NC}"
        exit 1
    fi
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}服务状态:${NC}"
    echo ""
    echo -e "${YELLOW}内置数据库服务:${NC}"
    docker-compose ps 2>/dev/null || echo "未运行"
    echo ""
    echo -e "${YELLOW}外部数据库服务:${NC}"
    docker-compose -f docker-compose.external.yml ps 2>/dev/null || echo "未运行"
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看日志 (按 Ctrl+C 退出):${NC}"
    echo -e "${YELLOW}选择要查看的服务日志:${NC}"
    echo "1. 内置数据库服务"
    echo "2. 外部数据库服务"
    read -p "请选择 (1-2): " log_choice

    case $log_choice in
        1)
            docker-compose logs -f
            ;;
        2)
            docker-compose -f docker-compose.external.yml logs -f
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 清理镜像和容器
cleanup() {
    echo -e "${YELLOW}警告: 这将删除所有相关的容器、镜像和数据卷！${NC}"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}清理中...${NC}"
        docker-compose down -v 2>/dev/null || true
        docker-compose -f docker-compose.external.yml down -v 2>/dev/null || true
        docker rmi ${IMAGE_NAME}:${VERSION} ${IMAGE_NAME}:latest 2>/dev/null || true
        docker system prune -f
        echo -e "${GREEN}✓ 清理完成！${NC}"
    else
        echo -e "${YELLOW}取消清理操作${NC}"
    fi
}

# 配置外部数据库连接
configure_external_db() {
    echo -e "${BLUE}配置外部数据库连接${NC}"
    echo ""

    # 检查是否存在.env文件
    if [ -f ".env" ]; then
        echo -e "${YELLOW}发现现有的 .env 文件${NC}"
        read -p "是否要覆盖现有配置? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}取消配置${NC}"
            return
        fi
    fi

    # 复制模板文件
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${GREEN}✓ 已创建 .env 配置文件${NC}"
    else
        echo -e "${RED}错误: 未找到 .env.example 模板文件${NC}"
        return
    fi

    echo ""
    echo -e "${YELLOW}请输入数据库连接信息:${NC}"

    # MySQL配置
    read -p "MySQL主机地址 [localhost]: " mysql_host
    mysql_host=${mysql_host:-localhost}

    read -p "MySQL端口 [3306]: " mysql_port
    mysql_port=${mysql_port:-3306}

    read -p "数据库名 [smart_dispatch]: " mysql_database
    mysql_database=${mysql_database:-smart_dispatch}

    read -p "MySQL用户名 [root]: " mysql_username
    mysql_username=${mysql_username:-root}

    read -s -p "MySQL密码: " mysql_password
    echo

    # Redis配置
    read -p "Redis主机地址 [localhost]: " redis_host
    redis_host=${redis_host:-localhost}

    read -p "Redis端口 [6379]: " redis_port
    redis_port=${redis_port:-6379}

    read -s -p "Redis密码 (可选): " redis_password
    echo

    # 更新.env文件
    sed -i "s/MYSQL_HOST=.*/MYSQL_HOST=${mysql_host}/" .env
    sed -i "s/MYSQL_PORT=.*/MYSQL_PORT=${mysql_port}/" .env
    sed -i "s/MYSQL_DATABASE=.*/MYSQL_DATABASE=${mysql_database}/" .env
    sed -i "s/MYSQL_USERNAME=.*/MYSQL_USERNAME=${mysql_username}/" .env
    sed -i "s/MYSQL_PASSWORD=.*/MYSQL_PASSWORD=${mysql_password}/" .env
    sed -i "s/REDIS_HOST=.*/REDIS_HOST=${redis_host}/" .env
    sed -i "s/REDIS_PORT=.*/REDIS_PORT=${redis_port}/" .env
    sed -i "s/REDIS_PASSWORD=.*/REDIS_PASSWORD=${redis_password}/" .env

    echo ""
    echo -e "${GREEN}✓ 外部数据库配置完成！${NC}"
    echo -e "${YELLOW}配置信息已保存到 .env 文件${NC}"
    echo ""
    echo -e "${YELLOW}数据库连接信息:${NC}"
    echo -e "MySQL: ${mysql_host}:${mysql_port}/${mysql_database}"
    echo -e "Redis: ${redis_host}:${redis_port}"
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-11): " choice
    echo ""

    case $choice in
        1)
            build_image
            ;;
        2)
            start_services
            ;;
        3)
            start_services_external
            ;;
        4)
            stop_services
            ;;
        5)
            rebuild_and_start
            ;;
        6)
            rebuild_and_start_external
            ;;
        7)
            show_status
            ;;
        8)
            show_logs
            ;;
        9)
            cleanup
            ;;
        10)
            configure_external_db
            ;;
        11)
            echo -e "${GREEN}感谢使用智慧派单管理系统！${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选项，请重新选择${NC}"
            ;;
    esac

    echo ""
    read -p "按回车键继续..."
    echo ""
done
