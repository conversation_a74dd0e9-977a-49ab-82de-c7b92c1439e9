# 使用官方PHP 8.1 Apache镜像作为基础镜像
FROM php:8.1-apache

# 设置工作目录
WORKDIR /var/www/html

# 只安装必要的PHP扩展，跳过系统包安装
RUN docker-php-ext-install pdo_mysql \
    && a2enmod rewrite

# 复制项目文件
COPY . /var/www/html/

# 设置Apache DocumentRoot为public目录
RUN sed -i 's|/var/www/html|/var/www/html/public|g' /etc/apache2/sites-available/000-default.conf \
    && sed -i 's|/var/www/html|/var/www/html/public|g' /etc/apache2/apache2.conf

# 设置文件权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/runtime \
    && chmod -R 777 /var/www/html/public/uploads

# 创建必要的目录
RUN mkdir -p /var/www/html/runtime/log \
    && mkdir -p /var/www/html/runtime/cache \
    && mkdir -p /var/www/html/runtime/temp

# 暴露端口
EXPOSE 80

# 启动Apache
CMD ["apache2-foreground"]
